.header {
    ul {
        margin: 0;
        padding: 0;
    }

    &.header--sticky {
        .header-content-sticky {
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1001;
            border-bottom: none;
            padding: 0;
            -webkit-animation: fadeInDown .6s both;
            animation: fadeInDown .6s both;

            .header-wrapper {
                padding: 10px 0;
                box-shadow: 0 0 10px rgb(0 0 0 / 20%);
            }

            &.header-middle, &.header-top {
                box-shadow: 0 0 10px rgb(0 0 0 / 20%);
                .header-wrapper {
                    box-shadow: none;
                }
            }
        }
    }

    .header-top {
        font-size: 13px;
        line-height: 1;
        padding: 10px 0;
        background-color: $top-header-background-color;
        border-bottom: 1px solid var(--header-deliver-color);

        .header-info-right {
            display: -webkit-box;
            display: -webkit-flex;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-pack: end;
            -webkit-justify-content: flex-end;
            -ms-flex-pack: end;
            justify-content: flex-end;
        }

        .header-info {

            & > ul {
                display: -webkit-box;
                display: -webkit-flex;
                display: -ms-flexbox;
                display: flex;

                & > li {
                    margin-right: 20px;
                    position: relative;
                    display: -webkit-box;
                    display: -webkit-flex;
                    display: -ms-flexbox;
                    display: flex;
                    -webkit-box-align: center;
                    -webkit-align-items: center;
                    -ms-flex-align: center;
                    align-items: center;

                    &:before {
                        content: '';
                        position: absolute;
                        right: -10px;
                        top: 50%;
                        -webkit-transform: translateY(-50%);
                        -ms-transform: translateY(-50%);
                        transform: translateY(-50%);
                        width: 1px;
                        height: 10px;
                        background: #dedfe2;
                    }

                    &:last-child {
                        margin-right: 0;

                        &:before {
                            display: none;
                        }

                    }

                    a {
                        color: $color-text;
                        font-weight: 500;

                        &:hover {
                            color: $color-primary;

                            span {
                                color: $color-primary;
                            }
                        }
                    }

                    .language-dropdown-active {
                        cursor: pointer;

                        &:hover {
                            color: $color-primary;
                        }

                        .svg-icon {
                            font-size: 0.9em;
                            margin-left: 2px;
                            margin-right: 5px;
                        }
                    }

                    & > ul {
                        &.language-dropdown {
                            position: absolute;
                            top: 100%;
                            left: 0;
                            z-index: 2;
                            min-width: 120px;
                            background: #fff;
                            -webkit-transform: translateY(20px);
                            transform: translateY(20px);
                            visibility: hidden;
                            opacity: 0;
                            -webkit-transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
                            transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
                            border: 1px solid $border-color;
                            padding: 0;
                            border-radius: 0;
                        }

                        li {
                            display: block;

                            a {
                                display: block;
                                color: #696969;
                                padding: 5px;

                                &:hover {
                                    color: $color-primary;
                                    background: none;
                                }

                                img {
                                    max-width: 15px;
                                    display: inline-block;
                                    margin-right: 5px;
                                }
                            }

                        }

                    }

                    &:hover {
                        & > a {
                            color: #333;
                        }

                        & > ul {
                            &.language-dropdown {
                                visibility: visible;
                                opacity: 1;
                                -webkit-transform: translateY(0);
                                -ms-transform: translateY(0);
                                transform: translateY(0);
                                top: 25px;
                            }

                        }

                    }

                }

            }

            i {
                font-size: 12px;
                margin-right: 5px;
                line-height: 6px;
            }
        }

    }

    .header-middle {
        background: $middle-header-background-color;

        .header-wrapper {
            display: flex;
            align-items: center;
            padding: 38.5px 0;

            .header-items {
                display: flex;
                align-items: center;
                height: 100%;
                flex: auto;
            }
        }

        .header__left {
            width: 17%;
            padding-right: 30px;

            .logo {
                flex: 0 1 auto;
                position: relative;
                z-index: 100;

                a {
                    position: relative;

                    img {
                        display: inline-block;
                        vertical-align: middle;
                    }
                }
            }
        }

        .header__center {
            width: 42.5%;

            .form--quick-search {
                display: flex;
                flex-flow: row nowrap;
                position: relative;
                width: 100%;

                .form-group--icon {
                    width: auto;
                    float: left;
                    cursor: pointer;
                    position: relative;
                    height: 50px;
                    line-height: 54px;

                    &:after {
                        content: '';
                        position: absolute;
                        right: 0;
                        top: 50%;
                        transform: translateY(-50%);
                        background-color: #c5c5c5;
                        width: 1px;
                        height: 23px;
                    }

                    .product-category-label {
                        padding-right: 30px;
                        padding-left: 15px;
                        border-right: none;
                        color: var(--middle-header-text-color);
                        position: relative;
                        height: 100%;
                        white-space: nowrap;
                        background-color: #f7f7f7;
                        text-transform: uppercase;
                        font-size: 13px;
                        font-weight: 600;
                        display: inline-block;

                        svg {
                            font-size: 9px;
                            position: absolute;
                            top: 51%;
                            transform: translateY(-50%);
                            right: 13px;
                        }
                    }

                    .form-control {
                        position: absolute;
                        top: 0;
                        left: 0;
                        opacity: 0;
                        height: 100%;
                        cursor: pointer;
                        min-height: 42px;
                        width: 100%;
                        z-index: 10;
                        -webkit-appearance: none;
                        -moz-appearance: none;
                        appearance: none;
                        padding-left: 10px;
                    }
                }

                input[type="text"] {
                    height: 50px;
                    border-top-width: 0;
                    border-bottom-width: 0;
                    padding: 12px 23px;
                    font-size: 13px;
                    color: var(--middle-header-text-color);;
                    background-color: #f7f7f7;
                    border-right: none;
                    border-left: none;
                    outline: none !important;
                    box-shadow: none !important;
                    border-radius: 0;
                }

                button {
                    background-color: #f7f7f7;
                    border-radius: 0;

                    &.loading {
                        opacity: 1;
                    }
                }
            }
        }

        .header__right {
            width: 40.5%;
            text-align: right;
            display: flex;
            align-items: center;
            height: 100%;
            flex: auto;
            justify-content: flex-end;
            padding-left: 30px;

            > * {
                outline: none;
                text-align: left;
            }

            .header__extra {
                display: flex;
                align-items: center;

                .header-box-content {
                    text-align: right;

                    span {
                        font-size: 22px;
                        font-weight: 700;
                        line-height: 22px;
                        margin-bottom: 5px;
                        color: var(--middle-header-text-color);
                    }

                    p {
                        color: var(--header-text-secondary-color);
                        font-size: 13px;
                        font-weight: 400;
                        margin: 0;
                    }
                }

                &.header-compare {
                    position: relative;
                    margin: 0 15px 0 108px;
                    line-height: 1;

                    a {
                        position: relative;
                        display: inline-flex;
                        align-items: center;
                    }
                }

                &.header-wishlist {
                    margin-right: 15px;
                    line-height: 1;

                    a {
                        position: relative;
                        display: inline-block;
                        align-items: center;
                    }
                }

                .cart-text {
                    margin-left: 12px;
                    line-height: 1;

                    .cart-title {
                        line-height: 1;
                        display: block;
                        margin-bottom: 7px;
                        font-size: 11px;
                        color: var(--header-text-secondary-color);
                    }

                    .cart-price-total {
                        .cart-amount {
                            color: var(--middle-header-text-color);
                            font-weight: 700;
                            font-size: 16px;
                            line-height: 1;
                            display: block;
                        }
                    }
                }

                > a {
                    position: relative;
                    display: inline-flex;
                    align-items: center;
                }

                .svg-icon, i {
                    color: var(--middle-header-text-color);
                    font-size: 24px;
                    padding: 0 0 0 3px;
                }
            }

            .header-item-counter {
                position: absolute;
                top: -14px !important;
                right: -8px !important;
                width: 14px !important;
                height: 14px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                text-align: center !important;
                background-color: var(--primary-button-background-color) !important;
                border-radius: 50% !important;
                font-size: 9px !important;
                font-weight: 700 !important;
                color: $primary-button-color !important;
                line-height: 1 !important;
                z-index: 2 !important;
            }
        }
    }

    .header-bottom {
        background: $bottom-header-background-color;

        .header-wrapper {
            padding: 10px 0 12px;
            position: relative;

            .navigation {
                > .container-xxxl {
                    display: flex;
                    flex-flow: row nowrap;

                    > * {
                        width: 100%;
                    }
                }

                &__extra {
                    > li {
                        position: relative;
                        display: inline-block;
                        margin-right: 20px;
                        padding-right: 20px;

                        &:after {
                            content: '';
                            @include vertical-align();
                            right: 0;
                            width: 2px;
                            height: 15px;
                            background-color: #000;
                        }

                        a {
                            color: #000;
                        }

                        &:last-child {
                            margin-right: 0;
                            padding-right: 0;

                            &:after {
                                display: none;
                            }
                        }
                    }
                }

                .navigation__left {
                    width: auto;
                }

                .navigation__center {
                    display: flex;
                    flex-flow: row nowrap;
                    justify-content: flex-start;
                    align-items: center;
                    padding-left: 35px;
                }

                .navigation__right {
                    width: auto;
                }
            }
        }
    }

    .header-item-counter {
        position: absolute;
        top: -14px !important;
        right: -8px !important;
        width: 14px !important;
        height: 14px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        text-align: center !important;
        background-color: var(--primary-button-background-color) !important;
        border-radius: 50% !important;
        font-size: 9px !important;
        font-weight: 700 !important;
        color: $primary-button-color !important;
        line-height: 1 !important;
        z-index: 2 !important;
    }

    .header-mobile {
        .menu-mobile {
            .menu-mobile-wrapper {
                width: 82%;
                position: fixed;
                top: 0;
                left: -82%;
                background-color: #fff;
                bottom: 0;
                overflow: hidden;
                z-index: 9999;
                transition: transform .25s ease;
            }

            .menu-icon {
                display: inline-flex;
                font-size: 24px;
                cursor: pointer;
            }
        }
    }
}

// New Mobile Header Styles - Show on mobile, hide on desktop
.header-mobile-new {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    position: absolute !important;
    left: -9999px !important;

    &.header--sticky {
        position: fixed;
        top: 0;
        width: 100%;
        z-index: 1000;
        border-bottom: none;
        padding: 10px 15px;
        box-shadow: 0 0 10px rgb(0 0 0 / 20%);
        -webkit-animation: fadeInDown .6s both;
        animation: fadeInDown .6s both;
    }
}

.show-admin-bar {
    .header {
        &.header--sticky {
            .header-content-sticky {
                top: 40px;
            }
        }
    }

    .header-mobile-new {
        &.header--sticky {
            top: 40px;
        }
    }
}

@media (max-width: 1519px) {
    .header {
        .header-middle {
            .header__left {
                width: 20%;
            }

            .header__right {
                .header__extra {
                    &.header-compare {
                        margin-left: 30px;
                    }
                }
            }
        }
    }
}

@media (max-width: 1199px) {
    .header {
        .header-middle {
            .header__right {
                .header__extra {
                    &.header-compare, &.header-wishlist {
                        display: none;
                    }
                }
            }
        }
    }
}

@media (min-width: 1200px) {
    .header-mobile {
        display: none;
    }
}

@media (max-width: 992px) {
    // Add padding to body to account for mobile header and remove gaps
    body {
        padding-top: 30px !important;
        margin-top: 0 !important;
    }

    html {
        margin-top: 0 !important;
        padding-top: 0 !important;
    }

    // Aggressive gap removal - target all direct body children except header
    body > *:not(.header-mobile-new) {
        margin-top: 0 !important;
        padding-top: 0 !important;
    }

    // Remove any top margins/padding from content areas
    .main-content, .page-content, .container, .content-area,
    .page-wrapper, .site-content, .content-wrapper,
    main, section, article, .row, .col, .breadcrumb,
    .page-header, .content, .site-main, .primary,
    .entry-content, .post, .product, .shop, .archive {
        margin-top: 0 !important;
        padding-top: 0 !important;
    }

    // Target specific theme elements
    .page-title, .page-breadcrumb, .breadcrumb-wrapper,
    .hero-section, .banner, .slider, .carousel {
        margin-top: 0 !important;
        padding-top: 0 !important;
    }

    // Force content to start immediately after header
    .header-mobile-new + *,
    body > *:not(.header-mobile-new):first-of-type {
        margin-top: 0px !important;
    }

    // Additional targeting for common content wrappers
    .page-wrapper, .site-content, .main-content,
    .content-area, .primary, .site-main {
        margin-top: 0px !important;
    }

    // Target all possible content containers
    body > div:not(.header-mobile-new),
    body > main:not(.header-mobile-new),
    body > section:not(.header-mobile-new) {
        margin-top: 0px !important;
    }

    // Ultra aggressive targeting
    body > *:not(.header-mobile-new) {
        margin-top: 0px !important;
    }

    // Force sticky add-to-cart to be visible in mobile view - Override all existing styles
    #sticky-add-to-cart,
    #sticky-add-to-cart .sticky-atc-wrap,
    .sticky-add-to-cart,
    .sticky-atc-wrap,
    .sticky-bar,
    .product-sticky-bar {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        position: fixed !important;
        bottom: 60px !important;
        left: 0 !important;
        right: 0 !important;
        z-index: 9999 !important;
        background: #fff !important;
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1) !important;
        width: 100% !important;
        height: auto !important;
        overflow: visible !important;
        transform: translate3d(0, 0, 0) !important;
        padding: 10px 0 !important;
        border-bottom: 1px solid #eee !important;
    }

    // Ensure sticky add-to-cart content and buttons are visible
    #sticky-add-to-cart *,
    #sticky-add-to-cart .sticky-atc-wrap *,
    .sticky-atc-wrap * {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    // Force sticky buttons to be visible
    #sticky-add-to-cart .sticky-atc-btn,
    #sticky-add-to-cart .btn,
    .sticky-atc-wrap .btn {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    // Fix mobile menu scroll lock issue
    body.menu-open,
    body.mobile-menu-open,
    body.nav-open,
    body.sidebar-open {
        overflow: auto !important;
        position: static !important;
        height: auto !important;
        width: auto !important;
    }

    // Ensure mobile menu doesn't prevent scrolling
    .mobile-menu-overlay,
    .menu-overlay,
    .sidebar-overlay {
        position: fixed !important;
        overflow-y: auto !important;
        -webkit-overflow-scrolling: touch !important;
    }

    // Allow scrolling in mobile menu
    .mobile-menu,
    .sidebar-menu,
    .nav-menu {
        overflow-y: auto !important;
        -webkit-overflow-scrolling: touch !important;
        max-height: 100vh !important;
    }

    // Fix mobile menu sidebar positioning - make it full height
    .panel--sidebar,
    #menu-mobile,
    .menu-mobile-wrapper {
        top: 0 !important;
        bottom: 0 !important;
        height: 100vh !important;
        min-height: 100vh !important;
        max-height: 100vh !important;
        z-index: 99999 !important;
        position: fixed !important;
    }

    // Ensure menu content accounts for header space and fills full height
    .panel__content {
        padding-top: 80px !important;
        padding-bottom: 20px !important;
        height: 100vh !important;
        min-height: 100vh !important;
        overflow-y: auto !important;
        box-sizing: border-box !important;
    }

    .header {
        .header-middle, .header-middle.header-content-sticky {
            border-bottom: none;

            .header-wrapper {
                padding: 0;

                .header__left {
                    display: none;
                }

                .header__center {
                    display: none;
                }

                .header__right {
                    .header-support {
                        display: none;
                    }

                    .cart--mini {
                        .header__extra {
                            display: none;
                        }
                    }
                }
            }
        }
    }

    .header-bottom {
        display: none;
    }

    // Hide old mobile header
    .header-mobile {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
        position: absolute !important;
        left: -9999px !important;
    }

    // New Mobile Header Styles - Clean White Design
    .header-mobile-new {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
        position: fixed !important;
        left: 0 !important;
        top: 0;
        right: 0;
        background: #ffffff !important;
        width: 100% !important;
        height: 60px !important;
        z-index: 99999 !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
        border-bottom: none !important;
        padding: 0 !important;
    }

    .mobile-header-container {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        padding: 0 16px;
        max-width: 100%;
    }

    .mobile-header-left {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 100%;
        flex-shrink: 0;
    }

    .mobile-header-center {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        margin-left: 12px;
    }

    .mobile-header-right {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 100%;
        flex-shrink: 0;
    }

    .mobile-header-center {
        margin-right: 12px;
    }

    .mobile-back-button {
        .back-link {
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
            text-decoration: none;
            width: 32px;
            height: 32px;
            border-radius: 4px;
            margin: auto;
            transition: background-color 0.2s ease;

            &:hover {
                background-color: #f5f5f5;
            }

            .svg-icon {
                font-size: 18px;
                color: #333;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }
    }

    .mobile-menu-trigger {
        .menu-icon-wrapper {
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            width: 32px;
            height: 32px;
            border-radius: 4px;
            margin: auto;
            transition: background-color 0.2s ease;

            &:hover {
                background-color: #f5f5f5;
            }

            .svg-icon {
                font-size: 18px;
                color: #333;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }
    }

    .mobile-search-form {
        width: 100%;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;

        .form--quick-search {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
        }
    }

    // Homepage specific larger styles
    body.home & {
        .mobile-search-form {
            height: 50px;

            .form--quick-search {
                height: 100%;
            }

            .search-input-wrapper {
                height: 50px;
                border-radius: 25px;

                .form-control {
                    line-height: 50px;
                    height: 50px;
                    font-size: 16px;
                    padding: 0 70px 0 20px;
                }

                .search-submit-btn {
                    width: 60px;
                    height: 100%;
                    border-radius: 0 25px 25px 0;
                    right: 0;
                    top: 0;
                    bottom: 0;
                    box-shadow: 0 3px 12px rgba(255, 102, 51, 0.4);

                    &:hover {
                        box-shadow: 0 5px 16px rgba(255, 102, 51, 0.5);
                    }

                    .svg-icon {
                        font-size: 18px;
                    }
                }
            }
        }

        .mobile-menu-trigger {
            .menu-icon-wrapper {
                width: 40px;
                height: 40px;

                .svg-icon {
                    font-size: 24px;
                }
            }
        }
    }

    .mobile-search-form {
        .search-input-wrapper {
            display: flex;
            position: relative;
            width: 100%;
            height: 100%;
            background: #ffffff;
            border-radius: 20px;
            overflow: hidden;
            border: 2px solid #000000;
            transition: border-color 0.2s ease;

            &:hover {
                border-color: #333333;
            }

            &:focus-within {
                border-color: #ff6633;
                box-shadow: 0 0 0 2px rgba(255, 102, 51, 0.2);
            }

            .form-control {
                border: none;
                background: transparent;
                padding: 0 60px 0 16px;
                font-size: 14px;
                font-weight: 400;
                line-height: 40px;
                height: 40px;
                width: 100%;
                outline: none;
                color: #333;

                &::placeholder {
                    color: #999;
                    font-weight: 400;
                }

                &:focus {
                    box-shadow: none;
                    outline: none;
                }
            }

            .search-submit-btn {
                position: absolute;
                right: 0;
                top: 0;
                bottom: 0;
                transform: none;
                background: linear-gradient(135deg, #ff6633 0%, #e55a2b 100%);
                border: none;
                width: 50px;
                height: 100%;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 0 20px 20px 0;
                box-shadow: 0 2px 8px rgba(255, 102, 51, 0.3);
                transition: all 0.2s ease;

                &:hover {
                    background: linear-gradient(135deg, #e55a2b 0%, #d14d1f 100%);
                    box-shadow: 0 4px 12px rgba(255, 102, 51, 0.4);
                }

                &:active {
                    background: linear-gradient(135deg, #d14d1f 0%, #b8421a 100%);
                    box-shadow: 0 1px 4px rgba(255, 102, 51, 0.3);
                }

                .svg-icon {
                    font-size: 16px;
                    color: #fff;
                    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
                }
            }
        }
    }


}

// Additional responsive styles for new mobile header
@media (max-width: 768px) {
    .header-mobile-new {
        .mobile-header-container {
            padding: 0 12px;
        }

        .mobile-header-left {
            width: 36px;
        }

        .mobile-header-center {
            margin-left: 10px;
        }

        .mobile-search-form {
            height: 38px;

            .search-input-wrapper {
                border-radius: 19px;

                .form-control {
                    line-height: 38px;
                    height: 38px;
                    font-size: 13px;
                    padding: 0 46px 0 14px;
                }

                .search-submit-btn {
                    width: 34px;
                    height: 34px;
                    border-radius: 17px;

                    .svg-icon {
                        font-size: 15px;
                    }
                }
            }
        }

        .mobile-back-button .back-link,
        .mobile-menu-trigger .menu-icon-wrapper {
            width: 30px;
            height: 30px;

            .svg-icon {
                font-size: 16px;
            }
        }
    }
}

@media (max-width: 480px) {
    .header-mobile-new {
        .mobile-header-container {
            padding: 0 10px;
        }

        .mobile-header-left {
            width: 32px;
        }

        .mobile-header-center {
            margin-left: 8px;
        }

        .mobile-search-form {
            height: 36px;

            .search-input-wrapper {
                border-radius: 18px;

                .form-control {
                    line-height: 36px;
                    height: 36px;
                    font-size: 12px;
                    padding: 0 44px 0 12px;
                }

                .search-submit-btn {
                    width: 32px;
                    height: 32px;
                    border-radius: 16px;

                    .svg-icon {
                        font-size: 14px;
                    }
                }
            }
        }

        .mobile-back-button .back-link,
        .mobile-menu-trigger .menu-icon-wrapper {
            width: 28px;
            height: 28px;

            .svg-icon {
                font-size: 15px;
            }
        }
    }
}

// Ensure mobile header doesn't show on desktop and remove body padding
@media (min-width: 993px) {
    body {
        padding-top: 0 !important;
    }

    .header-mobile-new {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
        position: absolute !important;
        left: -9999px !important;
        top: -9999px !important;
        width: 0 !important;
        height: 0 !important;
        overflow: hidden !important;
    }

    // Force desktop header to show properly from 993px onwards
    .header {
        display: block !important;

        .header-top {
            display: block !important;
        }

        .header-middle {
            display: block !important;

            .header-wrapper {
                display: flex !important;
                align-items: center !important;
                padding: 15px 0 !important;

                .header__left {
                    display: block !important;
                }

                .header__center {
                    display: block !important;
                }

                .header__right {
                    display: block !important;

                    .header-support {
                        display: block !important;
                    }

                    .cart--mini {
                        .header__extra {
                            display: block !important;
                        }
                    }
                }
            }
        }

        .header-bottom {
            display: block !important;
        }
    }

    // Hide all mobile sticky elements from 993px onwards
    .sticky-bottom,
    .mobile-sticky-bottom,
    .bottom-sticky,
    .sticky-mobile-bottom,
    .mobile-bottom-bar,
    .bottom-navigation,
    .mobile-navigation-bottom,
    .sticky-navigation-bottom,
    .footer-mobile,
    .menu--footer,
    #sticky-add-to-cart {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
        position: absolute !important;
        left: -9999px !important;
        top: -9999px !important;
        width: 0 !important;
        height: 0 !important;
        overflow: hidden !important;
        z-index: -1 !important;
    }
}

@media (max-width: 1280px) {
    .header .header-middle .header__right .header__extra .header-box-content span {
        font-size: 20px;
    }
}

@keyframes fadeInDown {
    0% {
        opacity: 0;
        transform: translate3d(0, -100%, 0)
    }
    to {
        opacity: 1;
        transform: translateZ(0)
    }
}

.icon-badge-wrapper {
    position: relative;
    display: inline-block;
}

// Ultra-specific override for header badge
.header .icon-badge-wrapper .header-item-counter {
  position: absolute !important;
  top: -14px !important;
  left: -8px !important;
  width: 14px !important;
  height: 14px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  text-align: center !important;
  background-color: var(--primary-button-background-color) !important;
  border-radius: 50% !important;
  font-size: 9px !important;
  font-weight: 700 !important;
  color: $primary-button-color !important;
  line-height: 1 !important;
  z-index: 2 !important;
}
